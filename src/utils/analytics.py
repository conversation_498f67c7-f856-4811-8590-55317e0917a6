"""
Dataset Analytics Module
=======================

Comprehensive analytics and visualization tools for dataset analysis.
"""

import logging
from typing import List, Dict, Tuple, Optional
import numpy as np
import pandas as pd
from collections import Counter, defaultdict
from pathlib import Path
import json

try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False
    logging.warning("Matplotlib/Seaborn not available. Plotting functionality disabled.")


class DatasetAnalytics:
    """
    Comprehensive dataset analytics with visualization capabilities.
    """
    
    def __init__(self, config: Dict):
        """
        Initialize dataset analytics.
        
        Args:
            config: Configuration dictionary
        """
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.plotting_enabled = PLOTTING_AVAILABLE and config.get('generate_plots', True)
    
    def generate_comprehensive_analytics(self, metadata: List[Dict], output_path: Path):
        """
        Generate comprehensive analytics for the dataset.
        
        Args:
            metadata: List of metadata dictionaries
            output_path: Output directory path
        """
        self.logger.info("Generating comprehensive dataset analytics")
        
        # Create analytics directory
        analytics_dir = output_path / 'analytics'
        analytics_dir.mkdir(exist_ok=True)
        
        # Convert to DataFrame for easier analysis
        df = pd.DataFrame(metadata)
        
        # Generate various analytics
        analytics_results = {}
        
        # Basic statistics
        analytics_results['basic_stats'] = self._calculate_basic_statistics(df)
        
        # Category analysis
        analytics_results['category_analysis'] = self._analyze_categories(df)
        
        # Size and dimension analysis
        analytics_results['size_analysis'] = self._analyze_sizes(df)
        analytics_results['dimension_analysis'] = self._analyze_dimensions(df)
        
        # Split analysis
        analytics_results['split_analysis'] = self._analyze_splits(df)
        
        # Quality analysis
        analytics_results['quality_analysis'] = self._analyze_quality(df)
        
        # Format analysis
        analytics_results['format_analysis'] = self._analyze_formats(df)
        
        # Save analytics results
        self._save_analytics_results(analytics_results, analytics_dir)
        
        # Generate visualizations if available
        if self.plotting_enabled:
            self._generate_visualizations(df, analytics_dir)
        
        # Generate summary report
        self._generate_summary_report(analytics_results, analytics_dir)
        
        self.logger.info("Analytics generation completed")
    
    def _calculate_basic_statistics(self, df: pd.DataFrame) -> Dict:
        """
        Calculate basic dataset statistics.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Dictionary with basic statistics
        """
        return {
            'total_images': len(df),
            'unique_categories': df['primary_category'].nunique(),
            'unique_formats': df['format'].nunique(),
            'processed_images': df['processed'].sum() if 'processed' in df.columns else 0,
            'high_confidence_predictions': df['high_confidence'].sum() if 'high_confidence' in df.columns else 0,
            'average_file_size_mb': df['size_bytes'].mean() / (1024 * 1024),
            'total_dataset_size_gb': df['size_bytes'].sum() / (1024 * 1024 * 1024)
        }
    
    def _analyze_categories(self, df: pd.DataFrame) -> Dict:
        """
        Analyze category distribution and balance.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Dictionary with category analysis
        """
        category_counts = df['primary_category'].value_counts()
        
        # Calculate balance metrics
        max_count = category_counts.max()
        min_count = category_counts.min()
        balance_ratio = min_count / max_count if max_count > 0 else 0
        
        # Calculate entropy for diversity measure
        proportions = category_counts / category_counts.sum()
        entropy = -np.sum(proportions * np.log2(proportions + 1e-10))
        max_entropy = np.log2(len(category_counts))
        normalized_entropy = entropy / max_entropy if max_entropy > 0 else 0
        
        return {
            'category_distribution': category_counts.to_dict(),
            'most_common_category': category_counts.index[0],
            'least_common_category': category_counts.index[-1],
            'balance_ratio': balance_ratio,
            'diversity_score': normalized_entropy,
            'categories_with_single_image': (category_counts == 1).sum(),
            'imbalance_severity': self._calculate_imbalance_severity(category_counts)
        }
    
    def _analyze_sizes(self, df: pd.DataFrame) -> Dict:
        """
        Analyze file size distribution.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Dictionary with size analysis
        """
        sizes_mb = df['size_bytes'] / (1024 * 1024)
        
        return {
            'size_statistics': {
                'min_mb': sizes_mb.min(),
                'max_mb': sizes_mb.max(),
                'mean_mb': sizes_mb.mean(),
                'median_mb': sizes_mb.median(),
                'std_mb': sizes_mb.std(),
                'q25_mb': sizes_mb.quantile(0.25),
                'q75_mb': sizes_mb.quantile(0.75)
            },
            'size_category_distribution': df['size_category'].value_counts().to_dict() if 'size_category' in df.columns else {},
            'outliers': {
                'very_small': (sizes_mb < 0.01).sum(),  # < 10KB
                'very_large': (sizes_mb > 10).sum()     # > 10MB
            }
        }
    
    def _analyze_dimensions(self, df: pd.DataFrame) -> Dict:
        """
        Analyze image dimension distribution.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Dictionary with dimension analysis
        """
        if 'original_width' not in df.columns or 'original_height' not in df.columns:
            return {'error': 'Dimension data not available'}
        
        # Filter out zero dimensions
        valid_dims = df[(df['original_width'] > 0) & (df['original_height'] > 0)]
        
        if len(valid_dims) == 0:
            return {'error': 'No valid dimension data'}
        
        widths = valid_dims['original_width']
        heights = valid_dims['original_height']
        aspect_ratios = widths / heights
        
        return {
            'width_statistics': {
                'min': widths.min(),
                'max': widths.max(),
                'mean': widths.mean(),
                'median': widths.median(),
                'std': widths.std()
            },
            'height_statistics': {
                'min': heights.min(),
                'max': heights.max(),
                'mean': heights.mean(),
                'median': heights.median(),
                'std': heights.std()
            },
            'aspect_ratio_statistics': {
                'min': aspect_ratios.min(),
                'max': aspect_ratios.max(),
                'mean': aspect_ratios.mean(),
                'median': aspect_ratios.median(),
                'std': aspect_ratios.std()
            },
            'dimension_category_distribution': df['dimension_category'].value_counts().to_dict() if 'dimension_category' in df.columns else {},
            'orientation_distribution': df['orientation'].value_counts().to_dict() if 'orientation' in df.columns else {},
            'common_resolutions': self._find_common_resolutions(valid_dims)
        }
    
    def _analyze_splits(self, df: pd.DataFrame) -> Dict:
        """
        Analyze dataset split distribution.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Dictionary with split analysis
        """
        if 'split' not in df.columns:
            return {'error': 'Split data not available'}
        
        split_counts = df['split'].value_counts()
        total = len(df)
        
        # Analyze balance across splits for each category
        category_split_balance = {}
        for category in df['primary_category'].unique():
            category_data = df[df['primary_category'] == category]
            category_splits = category_data['split'].value_counts()
            category_split_balance[category] = category_splits.to_dict()
        
        return {
            'split_distribution': split_counts.to_dict(),
            'split_percentages': (split_counts / total * 100).to_dict(),
            'category_split_balance': category_split_balance,
            'split_balance_score': self._calculate_split_balance_score(df)
        }
    
    def _analyze_quality(self, df: pd.DataFrame) -> Dict:
        """
        Analyze image quality metrics.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Dictionary with quality analysis
        """
        quality_metrics = {}
        
        if 'prediction_confidence' in df.columns:
            confidence_scores = df['prediction_confidence']
            quality_metrics['confidence_statistics'] = {
                'mean': confidence_scores.mean(),
                'median': confidence_scores.median(),
                'std': confidence_scores.std(),
                'high_confidence_ratio': (confidence_scores >= self.config.get('confidence_threshold', 0.5)).mean()
            }
        
        if 'enhanced' in df.columns:
            quality_metrics['enhancement_ratio'] = df['enhanced'].mean()
        
        if 'normalized' in df.columns:
            quality_metrics['normalization_ratio'] = df['normalized'].mean()
        
        return quality_metrics
    
    def _analyze_formats(self, df: pd.DataFrame) -> Dict:
        """
        Analyze file format distribution.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Dictionary with format analysis
        """
        format_counts = df['format'].value_counts()
        
        return {
            'format_distribution': format_counts.to_dict(),
            'format_percentages': (format_counts / len(df) * 100).to_dict(),
            'most_common_format': format_counts.index[0],
            'format_diversity': len(format_counts)
        }
    
    def _calculate_imbalance_severity(self, category_counts: pd.Series) -> str:
        """
        Calculate the severity of class imbalance.
        
        Args:
            category_counts: Series with category counts
            
        Returns:
            Imbalance severity level
        """
        if len(category_counts) <= 1:
            return 'none'
        
        max_count = category_counts.max()
        min_count = category_counts.min()
        ratio = max_count / min_count
        
        if ratio <= 2:
            return 'low'
        elif ratio <= 5:
            return 'moderate'
        elif ratio <= 10:
            return 'high'
        else:
            return 'severe'
    
    def _find_common_resolutions(self, df: pd.DataFrame, top_n: int = 10) -> Dict:
        """
        Find most common image resolutions.
        
        Args:
            df: DataFrame with dimension data
            top_n: Number of top resolutions to return
            
        Returns:
            Dictionary with common resolutions
        """
        resolutions = df.apply(lambda row: f"{row['original_width']}x{row['original_height']}", axis=1)
        resolution_counts = resolutions.value_counts().head(top_n)
        return resolution_counts.to_dict()
    
    def _calculate_split_balance_score(self, df: pd.DataFrame) -> float:
        """
        Calculate a balance score for dataset splits across categories.
        
        Args:
            df: DataFrame containing metadata
            
        Returns:
            Balance score between 0 and 1 (1 = perfectly balanced)
        """
        if 'split' not in df.columns:
            return 0.0
        
        balance_scores = []
        
        for category in df['primary_category'].unique():
            category_data = df[df['primary_category'] == category]
            split_counts = category_data['split'].value_counts()
            
            if len(split_counts) > 1:
                # Calculate coefficient of variation (lower is more balanced)
                cv = split_counts.std() / split_counts.mean()
                balance_score = 1 / (1 + cv)  # Convert to 0-1 scale
                balance_scores.append(balance_score)
        
        return np.mean(balance_scores) if balance_scores else 0.0
    
    def _save_analytics_results(self, results: Dict, output_dir: Path):
        """
        Save analytics results to files.
        
        Args:
            results: Analytics results dictionary
            output_dir: Output directory path
        """
        # Save as JSON
        json_path = output_dir / 'analytics_results.json'
        with open(json_path, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        # Save as YAML if available
        try:
            import yaml
            yaml_path = output_dir / 'analytics_results.yaml'
            with open(yaml_path, 'w') as f:
                yaml.dump(results, f, default_flow_style=False)
        except ImportError:
            pass
    
    def _generate_visualizations(self, df: pd.DataFrame, output_dir: Path):
        """
        Generate visualization plots.
        
        Args:
            df: DataFrame containing metadata
            output_dir: Output directory path
        """
        plt.style.use('default')
        
        # Category distribution plot
        if 'primary_category' in df.columns:
            plt.figure(figsize=(12, 6))
            category_counts = df['primary_category'].value_counts()
            plt.bar(range(len(category_counts)), category_counts.values)
            plt.xticks(range(len(category_counts)), category_counts.index, rotation=45, ha='right')
            plt.title('Category Distribution')
            plt.xlabel('Category')
            plt.ylabel('Count')
            plt.tight_layout()
            plt.savefig(output_dir / 'category_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
        
        # File size distribution
        if 'size_bytes' in df.columns:
            plt.figure(figsize=(10, 6))
            sizes_mb = df['size_bytes'] / (1024 * 1024)
            plt.hist(sizes_mb, bins=50, alpha=0.7, edgecolor='black')
            plt.title('File Size Distribution')
            plt.xlabel('Size (MB)')
            plt.ylabel('Frequency')
            plt.yscale('log')
            plt.tight_layout()
            plt.savefig(output_dir / 'size_distribution.png', dpi=300, bbox_inches='tight')
            plt.close()
    
    def _generate_summary_report(self, analytics_results: Dict, output_dir: Path):
        """
        Generate a comprehensive summary report.
        
        Args:
            analytics_results: Analytics results dictionary
            output_dir: Output directory path
        """
        report_path = output_dir / 'analytics_summary_report.txt'
        
        with open(report_path, 'w') as f:
            f.write("DATASET ANALYTICS SUMMARY REPORT\n")
            f.write("=" * 50 + "\n\n")
            
            # Basic statistics
            if 'basic_stats' in analytics_results:
                stats = analytics_results['basic_stats']
                f.write("BASIC STATISTICS\n")
                f.write("-" * 20 + "\n")
                f.write(f"Total Images: {stats.get('total_images', 0):,}\n")
                f.write(f"Unique Categories: {stats.get('unique_categories', 0)}\n")
                f.write(f"Unique Formats: {stats.get('unique_formats', 0)}\n")
                f.write(f"Average File Size: {stats.get('average_file_size_mb', 0):.2f} MB\n")
                f.write(f"Total Dataset Size: {stats.get('total_dataset_size_gb', 0):.2f} GB\n\n")
            
            # Category analysis
            if 'category_analysis' in analytics_results:
                cat_analysis = analytics_results['category_analysis']
                f.write("CATEGORY ANALYSIS\n")
                f.write("-" * 20 + "\n")
                f.write(f"Most Common Category: {cat_analysis.get('most_common_category', 'N/A')}\n")
                f.write(f"Least Common Category: {cat_analysis.get('least_common_category', 'N/A')}\n")
                f.write(f"Balance Ratio: {cat_analysis.get('balance_ratio', 0):.3f}\n")
                f.write(f"Diversity Score: {cat_analysis.get('diversity_score', 0):.3f}\n")
                f.write(f"Imbalance Severity: {cat_analysis.get('imbalance_severity', 'unknown')}\n\n")
            
            # Add more sections as needed...
            
        self.logger.info(f"Summary report generated: {report_path}")
