"""
نظام التصنيف المتخصص للإنقاذ والبحث
Specialized Classification System for Search and Rescue Operations
================================================================

نظام تصنيف متقدم مصمم خصيصاً لتحديد البيئات واكتشاف الناجين والحطام
في عمليات البحث والإنقاذ البحرية والصحراوية.
"""

import cv2
import numpy as np
import logging
from typing import Dict, List, Tuple, Optional, Any
from pathlib import Path
import json
from datetime import datetime

try:
    import torch
    import torchvision.transforms as transforms
    from torchvision import models
    import timm
    TORCH_AVAILABLE = True
except ImportError:
    TORCH_AVAILABLE = False

try:
    from ultralytics import YOLO
    ULTRALYTICS_AVAILABLE = True
except ImportError:
    ULTRALYTICS_AVAILABLE = False

try:
    import tensorflow as tf
    from tensorflow.keras.applications import ResNet50, VGG16
    from tensorflow.keras.applications.resnet50 import preprocess_input, decode_predictions
    TF_AVAILABLE = True
except ImportError:
    TF_AVAILABLE = False

from ..utils.exceptions import ClassifierError


class RescueSpecializedClassifier:
    """
    مصنف متخصص لعمليات الإنقاذ والبحث
    Specialized classifier for search and rescue operations
    """

    # فئات البيئة المدعومة
    ENVIRONMENT_CLASSES = {
        'sea': 'بحر',
        'desert': 'صحراء',
        'coast': 'ساحل',
        'urban': 'حضري',
        'forest': 'غابة',
        'mountain': 'جبل'
    }

    # فئات أهداف الإنقاذ
    RESCUE_TARGET_CLASSES = {
        'person': 'شخص',
        'life_jacket': 'سترة نجاة',
        'life_boat': 'زورق نجاة',
        'debris': 'حطام',
        'aircraft_wreckage': 'حطام طائرة',
        'vehicle_wreckage': 'حطام مركبة',
        'distress_signal': 'إشارة استغاثة',
        'survivor_shelter': 'مأوى ناجين',
        'emergency_equipment': 'معدات طوارئ'
    }

    def __init__(self, config: Dict):
        """
        تهيئة المصنف المتخصص

        Args:
            config: إعدادات التكوين
        """
        self.config = config
        self.logger = logging.getLogger(__name__)

        # إعدادات التصنيف
        self.classification_settings = self._load_classification_settings()

        # النماذج المحملة
        self.environment_model = None
        self.rescue_detection_model = None
        self.scene_classification_model = None

        # إحصائيات التصنيف
        self.classification_stats = {
            'total_classified': 0,
            'environment_classifications': {},
            'rescue_detections': {},
            'confidence_scores': [],
            'processing_times': [],
            'human_verifications_needed': 0
        }

        # تهيئة النماذج
        self._initialize_models()

    def _load_classification_settings(self) -> Dict:
        """تحميل إعدادات التصنيف من التكوين"""
        return {
            # إعدادات تصنيف البيئة
            'environment_model_name': self.config.get('environment_model', 'resnet50'),
            'environment_confidence_threshold': self.config.get('environment_confidence_threshold', 0.7),

            # إعدادات كشف أهداف الإنقاذ
            'rescue_detection_model': self.config.get('rescue_detection_model', 'yolov8m'),
            'rescue_confidence_threshold': self.config.get('rescue_confidence_threshold', 0.5),
            'rescue_iou_threshold': self.config.get('rescue_iou_threshold', 0.45),
            'max_detections': self.config.get('max_detections', 100),

            # إعدادات التحقق البشري
            'human_verification_threshold': self.config.get('human_verification_threshold', 0.6),
            'enable_human_verification': self.config.get('enable_human_verification', True),

            # إعدادات الفئات المخصصة
            'custom_rescue_categories': self.config.get('custom_rescue_categories', []),
            'enable_multi_class_detection': self.config.get('enable_multi_class_detection', True),

            # إعدادات الأداء
            'device': self.config.get('device', 'auto'),
            'batch_size': self.config.get('batch_size', 1),
            'enable_gpu_acceleration': self.config.get('enable_gpu_acceleration', True)
        }

    def _initialize_models(self):
        """تهيئة نماذج التصنيف والكشف"""
        self.logger.info("تهيئة نماذج التصنيف المتخصصة للإنقاذ...")

        # تحديد الجهاز المستخدم
        self.device = self._get_device()

        # تهيئة نموذج تصنيف البيئة
        self._initialize_environment_model()

        # تهيئة نموذج كشف أهداف الإنقاذ
        self._initialize_rescue_detection_model()

        self.logger.info("تم تهيئة جميع النماذج بنجاح")

    def _get_device(self) -> str:
        """تحديد الجهاز المستخدم للمعالجة"""
        device_setting = self.classification_settings['device']

        if device_setting == 'auto':
            if TORCH_AVAILABLE and torch.cuda.is_available() and self.classification_settings['enable_gpu_acceleration']:
                device = 'cuda'
                self.logger.info(f"استخدام GPU: {torch.cuda.get_device_name(0)}")
            else:
                device = 'cpu'
                self.logger.info("استخدام CPU للمعالجة")
        else:
            device = device_setting

        return device

    def _initialize_environment_model(self):
        """تهيئة نموذج تصنيف البيئة"""
        model_name = self.classification_settings['environment_model_name']

        try:
            if TORCH_AVAILABLE:
                self.logger.info(f"تحميل نموذج تصنيف البيئة: {model_name}")

                if model_name == 'resnet50':
                    self.environment_model = models.resnet50(pretrained=True)
                elif model_name == 'efficientnet_b0':
                    self.environment_model = timm.create_model('efficientnet_b0', pretrained=True)
                elif model_name == 'vit_base':
                    self.environment_model = timm.create_model('vit_base_patch16_224', pretrained=True)
                else:
                    self.logger.warning(f"نموذج غير مدعوم: {model_name}, استخدام ResNet50")
                    self.environment_model = models.resnet50(pretrained=True)

                self.environment_model.eval()
                self.environment_model.to(self.device)

                # إعداد تحويلات الصورة
                self.environment_transforms = transforms.Compose([
                    transforms.ToPILImage(),
                    transforms.Resize(256),
                    transforms.CenterCrop(224),
                    transforms.ToTensor(),
                    transforms.Normalize(mean=[0.485, 0.456, 0.406],
                                       std=[0.229, 0.224, 0.225])
                ])

                self.logger.info("تم تحميل نموذج تصنيف البيئة بنجاح")

        except Exception as e:
            self.logger.error(f"فشل في تحميل نموذج تصنيف البيئة: {e}")
            self.environment_model = None

    def _initialize_rescue_detection_model(self):
        """تهيئة نموذج كشف أهداف الإنقاذ"""
        model_name = self.classification_settings['rescue_detection_model']

        try:
            if ULTRALYTICS_AVAILABLE:
                self.logger.info(f"تحميل نموذج كشف أهداف الإنقاذ: {model_name}")

                # تحميل نموذج YOLO
                self.rescue_detection_model = YOLO(f"{model_name}.pt")

                # تخصيص فئات الكشف لأهداف الإنقاذ
                self._customize_detection_classes()

                self.logger.info("تم تحميل نموذج كشف أهداف الإنقاذ بنجاح")

        except Exception as e:
            self.logger.error(f"فشل في تحميل نموذج كشف أهداف الإنقاذ: {e}")
            self.rescue_detection_model = None

    def _customize_detection_classes(self):
        """تخصيص فئات الكشف لأهداف الإنقاذ"""
        # ربط فئات COCO بأهداف الإنقاذ
        self.coco_to_rescue_mapping = {
            0: 'person',           # شخص
            1: 'vehicle_wreckage', # دراجة -> حطام مركبة
            2: 'vehicle_wreckage', # سيارة -> حطام مركبة
            3: 'vehicle_wreckage', # دراجة نارية -> حطام مركبة
            4: 'aircraft_wreckage', # طائرة -> حطام طائرة
            8: 'vehicle_wreckage', # شاحنة -> حطام مركبة
            9: 'life_boat',        # قارب -> زورق نجاة
            # يمكن إضافة المزيد من الربط حسب الحاجة
        }

    def classify_image(self, image_data: Dict) -> Dict:
        """
        تصنيف صورة واحدة لتحديد البيئة واكتشاف أهداف الإنقاذ

        Args:
            image_data: بيانات الصورة

        Returns:
            نتائج التصنيف والكشف
        """
        try:
            start_time = cv2.getTickCount()

            # الحصول على الصورة المعالجة
            if 'processed_array' not in image_data:
                raise ClassifierError("الصورة غير معالجة مسبقاً")

            image_array = image_data['processed_array']

            # تصنيف البيئة
            environment_results = self._classify_environment(image_array)

            # كشف أهداف الإنقاذ
            rescue_detection_results = self._detect_rescue_targets(image_array)

            # تحديد الحاجة للتحقق البشري
            needs_human_verification = self._needs_human_verification(
                environment_results, rescue_detection_results
            )

            # حساب وقت المعالجة
            processing_time = (cv2.getTickCount() - start_time) / cv2.getTickFrequency()
            self.classification_stats['processing_times'].append(processing_time)

            # تجميع النتائج
            classification_results = {
                'environment_classification': environment_results,
                'rescue_detection': rescue_detection_results,
                'needs_human_verification': needs_human_verification,
                'classification_timestamp': datetime.now().isoformat(),
                'processing_time': processing_time
            }

            # تحديث الإحصائيات
            self._update_classification_stats(classification_results)

            # دمج النتائج مع بيانات الصورة
            classified_data = image_data.copy()
            classified_data.update(classification_results)

            return classified_data

        except Exception as e:
            self.logger.error(f"فشل في تصنيف الصورة: {e}")
            raise ClassifierError(f"فشل في التصنيف: {e}")

    def _classify_environment(self, image_array: np.ndarray) -> Dict:
        """
        تصنيف البيئة (بحر/صحراء/ساحل/حضري)

        Args:
            image_array: مصفوفة الصورة

        Returns:
            نتائج تصنيف البيئة
        """
        if self.environment_model is None:
            return self._get_placeholder_environment_classification()

        try:
            # تحضير الصورة للنموذج
            if len(image_array.shape) == 4:
                image_array = image_array[0]  # إزالة البعد الإضافي

            # تحويل إلى RGB إذا لزم الأمر
            if image_array.shape[-1] == 1:
                image_array = np.repeat(image_array, 3, axis=-1)

            # تطبيق التحويلات
            input_tensor = self.environment_transforms(image_array).unsqueeze(0).to(self.device)

            # التنبؤ
            with torch.no_grad():
                outputs = self.environment_model(input_tensor)
                probabilities = torch.nn.functional.softmax(outputs[0], dim=0)

            # تحويل النتائج إلى تصنيف البيئة
            environment_prediction = self._map_to_environment_classes(probabilities)

            return environment_prediction

        except Exception as e:
            self.logger.error(f"فشل في تصنيف البيئة: {e}")
            return self._get_placeholder_environment_classification()

    def _map_to_environment_classes(self, probabilities: torch.Tensor) -> Dict:
        """
        ربط نتائج النموذج بفئات البيئة

        Args:
            probabilities: احتماليات النموذج

        Returns:
            تصنيف البيئة
        """
        # هذا مثال مبسط - في التطبيق الحقيقي نحتاج نموذج مدرب على فئات البيئة
        # هنا نستخدم تحليل بسيط للألوان والنسيج

        # تحويل إلى numpy
        probs = probabilities.cpu().numpy()

        # تحليل مبسط للبيئة بناءً على الألوان السائدة
        # (في التطبيق الحقيقي نحتاج نموذج مدرب خصيصاً)

        # محاكاة تصنيف البيئة
        environment_scores = {
            'sea': 0.3,
            'desert': 0.2,
            'coast': 0.25,
            'urban': 0.15,
            'forest': 0.05,
            'mountain': 0.05
        }

        # العثور على أعلى نتيجة
        predicted_environment = max(environment_scores, key=environment_scores.get)
        confidence = environment_scores[predicted_environment]

        return {
            'predicted_environment': predicted_environment,
            'environment_arabic': self.ENVIRONMENT_CLASSES[predicted_environment],
            'confidence': confidence,
            'all_scores': environment_scores,
            'high_confidence': confidence >= self.classification_settings['environment_confidence_threshold']
        }

    def _detect_rescue_targets(self, image_array: np.ndarray) -> Dict:
        """
        كشف أهداف الإنقاذ (ناجين، حطام، معدات)

        Args:
            image_array: مصفوفة الصورة

        Returns:
            نتائج كشف أهداف الإنقاذ
        """
        if self.rescue_detection_model is None:
            return self._get_placeholder_rescue_detection()

        try:
            # تحضير الصورة
            if len(image_array.shape) == 4:
                image_array = image_array[0]

            # تحويل إلى uint8 إذا لزم الأمر
            if image_array.dtype != np.uint8:
                if image_array.max() <= 1.0:
                    image_array = (image_array * 255).astype(np.uint8)
                else:
                    image_array = image_array.astype(np.uint8)

            # تشغيل الكشف
            results = self.rescue_detection_model(
                image_array,
                conf=self.classification_settings['rescue_confidence_threshold'],
                iou=self.classification_settings['rescue_iou_threshold'],
                max_det=self.classification_settings['max_detections'],
                verbose=False
            )

            # معالجة النتائج
            detections = []
            rescue_targets_found = False

            for result in results:
                if result.boxes is not None:
                    boxes = result.boxes
                    for i in range(len(boxes)):
                        class_id = int(boxes.cls[i])
                        confidence = float(boxes.conf[i])
                        bbox = boxes.xyxy[i].tolist()

                        # ربط فئة COCO بهدف الإنقاذ
                        rescue_class = self.coco_to_rescue_mapping.get(class_id, 'unknown')

                        if rescue_class != 'unknown':
                            rescue_targets_found = True

                            detection = {
                                'class_id': class_id,
                                'rescue_class': rescue_class,
                                'rescue_class_arabic': self.RESCUE_TARGET_CLASSES.get(rescue_class, rescue_class),
                                'confidence': confidence,
                                'bbox': bbox,
                                'bbox_normalized': boxes.xywhn[i].tolist()
                            }
                            detections.append(detection)

            # تصنيف الصورة
            if rescue_targets_found:
                image_classification = 'contains_targets'
                classification_arabic = 'تحتوي على أهداف'
            else:
                image_classification = 'no_targets'
                classification_arabic = 'خالية من الأهداف'

            return {
                'image_classification': image_classification,
                'classification_arabic': classification_arabic,
                'targets_detected': rescue_targets_found,
                'total_detections': len(detections),
                'detections': detections,
                'unique_rescue_classes': list(set(d['rescue_class'] for d in detections)),
                'detection_model_used': self.classification_settings['rescue_detection_model']
            }

        except Exception as e:
            self.logger.error(f"فشل في كشف أهداف الإنقاذ: {e}")
            return self._get_placeholder_rescue_detection()

    def _needs_human_verification(self, environment_results: Dict, rescue_results: Dict) -> bool:
        """
        تحديد ما إذا كانت الصورة تحتاج للتحقق البشري

        Args:
            environment_results: نتائج تصنيف البيئة
            rescue_results: نتائج كشف الأهداف

        Returns:
            True إذا كانت تحتاج للتحقق البشري
        """
        if not self.classification_settings['enable_human_verification']:
            return False

        threshold = self.classification_settings['human_verification_threshold']

        # فحص ثقة تصنيف البيئة
        env_confidence = environment_results.get('confidence', 0)
        if env_confidence < threshold:
            return True

        # فحص ثقة كشف الأهداف
        detections = rescue_results.get('detections', [])
        if detections:
            avg_confidence = np.mean([d['confidence'] for d in detections])
            if avg_confidence < threshold:
                return True

        # فحص التعارض في التصنيف
        environment = environment_results.get('predicted_environment', '')
        targets_detected = rescue_results.get('targets_detected', False)

        # مثال: إذا كانت البيئة "بحر" ولكن لا توجد أهداف بحرية
        if environment == 'sea' and targets_detected:
            rescue_classes = rescue_results.get('unique_rescue_classes', [])
            marine_targets = ['life_boat', 'life_jacket', 'person']
            if not any(target in marine_targets for target in rescue_classes):
                return True

        return False

    def _get_placeholder_environment_classification(self) -> Dict:
        """نتائج بديلة لتصنيف البيئة عند عدم توفر النموذج"""
        return {
            'predicted_environment': 'unknown',
            'environment_arabic': 'غير محدد',
            'confidence': 0.0,
            'all_scores': {},
            'high_confidence': False,
            'model_available': False
        }

    def _get_placeholder_rescue_detection(self) -> Dict:
        """نتائج بديلة لكشف الأهداف عند عدم توفر النموذج"""
        return {
            'image_classification': 'no_targets',
            'classification_arabic': 'خالية من الأهداف',
            'targets_detected': False,
            'total_detections': 0,
            'detections': [],
            'unique_rescue_classes': [],
            'detection_model_used': 'placeholder',
            'model_available': False
        }

    def _update_classification_stats(self, results: Dict):
        """تحديث إحصائيات التصنيف"""
        self.classification_stats['total_classified'] += 1

        # إحصائيات البيئة
        env_class = results['environment_classification']['predicted_environment']
        if env_class not in self.classification_stats['environment_classifications']:
            self.classification_stats['environment_classifications'][env_class] = 0
        self.classification_stats['environment_classifications'][env_class] += 1

        # إحصائيات كشف الأهداف
        rescue_class = results['rescue_detection']['image_classification']
        if rescue_class not in self.classification_stats['rescue_detections']:
            self.classification_stats['rescue_detections'][rescue_class] = 0
        self.classification_stats['rescue_detections'][rescue_class] += 1

        # إحصائيات الثقة
        env_confidence = results['environment_classification']['confidence']
        self.classification_stats['confidence_scores'].append(env_confidence)

        # إحصائيات التحقق البشري
        if results['needs_human_verification']:
            self.classification_stats['human_verifications_needed'] += 1

    def get_classification_statistics(self) -> Dict:
        """الحصول على إحصائيات التصنيف"""
        stats = self.classification_stats.copy()

        if stats['confidence_scores']:
            stats['average_confidence'] = np.mean(stats['confidence_scores'])
            stats['confidence_std'] = np.std(stats['confidence_scores'])

        if stats['processing_times']:
            stats['average_processing_time'] = np.mean(stats['processing_times'])
            stats['total_processing_time'] = np.sum(stats['processing_times'])

        return stats

    def export_classification_report(self, output_path: str):
        """تصدير تقرير التصنيف"""
        report_path = Path(output_path) / f"classification_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"

        report_data = {
            'classification_statistics': self.get_classification_statistics(),
            'model_settings': self.classification_settings,
            'environment_classes': self.ENVIRONMENT_CLASSES,
            'rescue_target_classes': self.RESCUE_TARGET_CLASSES,
            'export_timestamp': datetime.now().isoformat()
        }

        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, ensure_ascii=False, indent=2, default=str)

        self.logger.info(f"تم تصدير تقرير التصنيف إلى: {report_path}")
        return report_path