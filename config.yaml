# Image Processing and Classification Configuration
# ================================================

# Scanner Settings
# ---------------
validate_mime_type: true  # Validate MIME types for additional security

# Processor Settings
# -----------------
target_size: [224, 224]           # Target image dimensions [width, height]
maintain_aspect_ratio: true       # Maintain aspect ratio when resizing
convert_to_grayscale: false       # Convert images to grayscale
normalize: true                   # Apply normalization for deep learning
enhance_quality: true             # Apply quality enhancements
batch_size: 32                    # Batch size for processing
brightness_factor: 1.0            # Brightness adjustment factor (1.0 = no change)
contrast_factor: 1.0              # Contrast adjustment factor (1.0 = no change)
sharpness_factor: 1.0             # Sharpness adjustment factor (1.0 = no change)
noise_reduction: true             # Apply noise reduction filter

# Classifier Settings
# ------------------
use_pretrained_model: true        # Use pretrained model for classification
model_name: "resnet50"            # Pretrained model name
confidence_threshold: 0.5         # Minimum confidence for high-confidence predictions
custom_categories: []             # Custom categories for classification
                                  # Example: ["photos", "graphics", "documents"]

# Size-based classification thresholds (in bytes)
size_thresholds:
  small: 100000      # < 100KB
  medium: 1000000    # < 1MB
  large: 5000000     # < 5MB

# Dimension-based classification thresholds [width, height]
dimension_thresholds:
  thumbnail: [150, 150]
  small: [512, 512]
  medium: [1024, 1024]
  large: [2048, 2048]

# Dataset Manager Settings
# -----------------------
split_ratios: [0.7, 0.2, 0.1]     # Train/validation/test split ratios
naming_pattern: "{category}_{index:06d}"  # File naming pattern
preserve_original_names: false    # Keep original filenames
copy_images: true                 # Copy images to dataset (vs. symlinks)
create_symlinks: false            # Create symlinks instead of copying
export_formats: ["csv", "json", "yaml"]  # Metadata export formats

# General Settings
# ---------------
resume: false                     # Resume interrupted processing
log_level: "INFO"                 # Logging level (DEBUG, INFO, WARNING, ERROR)
log_file: null                    # Optional log file path
