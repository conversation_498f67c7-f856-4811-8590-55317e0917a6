# Advanced Image Processing and Classification Application

A comprehensive Python application for automatic dataset generation with advanced image processing, multi-criteria classification, and comprehensive analytics.

## Features

### 🔍 **Directory Scanning System**
- Recursive scanning of directories and subdirectories
- Support for multiple image formats (JPEG, PNG, TIFF, BMP)
- Robust error handling for corrupted files
- Progress tracking and detailed statistics
- MIME type validation for security

### 🖼️ **Image Preprocessing Pipeline**
- Configurable image resizing with aspect ratio preservation
- Quality enhancement (brightness, contrast, sharpness adjustment)
- Noise reduction and filtering
- RGB to grayscale conversion
- Normalization for deep learning compatibility
- Batch processing for efficiency

### 🤖 **Enhanced Multi-Criteria Classification System**
- **File Size Classification**: Configurable thresholds (Small < 500KB, Medium 500KB-2MB, Large > 2MB)
- **Dimension Classification**: Resolution-based categories with advanced aspect ratio analysis
- **Enhanced Content Classification**: Deep learning models (ResNet50, EfficientNet-B0, MobileNetV2)
- **Object Detection Integration**: YOLO models (YOLOv5, YOLOv8) with configurable confidence thresholds
- **Meta-Classification**: "Contains Objects" vs "Object-Free" automatic categorization
- **Comprehensive Metadata**: Detailed classification results with bounding boxes and confidence scores
- **Backward Compatibility**: All legacy classification methods preserved and enhanced

### 📊 **Data Management and Export**
- Consistent naming conventions
- Comprehensive metadata generation
- Multiple export formats (CSV, JSON, YAML)
- Dataset manifest with statistics
- Organized directory structure

### 📈 **Dataset Analytics and Splitting**
- Automatic train/validation/test splitting
- Balanced distribution across classes
- Comprehensive dataset statistics
- Visualization plots and charts
- Stratified sampling support

## Installation

1. **Clone or download the application:**
```bash
git clone <repository-url>
cd image-processing-app
```

2. **Install dependencies:**
```bash
pip install -r requirements.txt
```

3. **Install GUI dependencies (for graphical interface):**
```bash
# Ubuntu/Debian
sudo apt-get install python3-tk

# CentOS/RHEL/Fedora
sudo dnf install python3-tkinter

# macOS (if needed)
brew install python-tk
```

4. **Optional: Install deep learning frameworks:**
```bash
# For PyTorch support
pip install torch torchvision

# For TensorFlow support
pip install tensorflow

# For additional models
pip install timm
```

## Quick Start

### GUI Interface (Recommended for Beginners)

Launch the graphical user interface:
```bash
python3 launch_gui.py
```

The GUI provides:
- Easy directory selection
- Visual configuration options
- Real-time progress monitoring
- Built-in help and presets

### Command Line Interface

#### Basic Usage

```bash
python3 main.py --input /path/to/images --output /path/to/dataset
```

#### With Custom Configuration

```bash
python3 main.py --input ./images --output ./dataset --config config.yaml
```

#### Advanced Usage

```bash
python3 main.py \
  --input /path/to/images \
  --output /path/to/dataset \
  --split-ratios 0.8 0.1 0.1 \
  --verbose
```

## Configuration

The application uses a YAML configuration file for detailed customization. Create a custom configuration:

```bash
cp config.yaml my_config.yaml
# Edit my_config.yaml as needed
python main.py --config my_config.yaml --input ./images --output ./dataset
```

### Key Configuration Options

```yaml
# Image processing
target_size: [224, 224]
maintain_aspect_ratio: true
enhance_quality: true
normalize: true

# Classification
use_pretrained_model: true
model_name: "resnet50"
confidence_threshold: 0.5
custom_categories: ["photos", "graphics", "documents"]

# Dataset creation
split_ratios: [0.7, 0.2, 0.1]
naming_pattern: "{category}_{index:06d}"
export_formats: ["csv", "json", "yaml"]
```

## Output Structure

The application creates a well-organized dataset structure:

```
output_directory/
├── train/
│   ├── category1/
│   │   ├── category1_000001.jpg
│   │   ├── category1_000002.jpg
│   │   └── ...
│   └── category2/
│       └── ...
├── val/
│   └── ...
├── test/
│   └── ...
├── metadata.csv
├── metadata.json
├── metadata.yaml
├── dataset_manifest.json
└── analytics/
    ├── analytics_results.json
    ├── analytics_summary_report.txt
    ├── category_distribution.png
    └── size_distribution.png
```

## Examples

### Example 1: Basic Photo Organization

```bash
python main.py \
  --input ~/Pictures \
  --output ~/Datasets/photos \
  --config examples/photo_config.yaml
```

### Example 2: Document Classification

```bash
python main.py \
  --input ~/Documents/scanned \
  --output ~/Datasets/documents \
  --split-ratios 0.8 0.1 0.1
```

### Example 3: Medical Image Processing

```bash
python main.py \
  --input /data/medical_images \
  --output /data/processed_medical \
  --config examples/medical_config.yaml \
  --verbose
```

## Advanced Features

### Custom Categories

Define custom categories in your configuration:

```yaml
custom_categories:
  - "portraits"
  - "landscapes"
  - "architecture"
  - "animals"
```

### Resume Interrupted Processing

```bash
python main.py --input ./images --output ./dataset --resume
```

### Quality Enhancement

Configure image enhancement parameters:

```yaml
enhance_quality: true
brightness_factor: 1.1
contrast_factor: 1.2
sharpness_factor: 1.1
noise_reduction: true
```

## API Usage

You can also use the application programmatically:

```python
from src.core.scanner import ImageScanner
from src.core.processor import ImageProcessor
from src.core.classifier import ImageClassifier
from src.core.dataset_manager import DatasetManager
from src.utils.config_manager import ConfigManager

# Load configuration
config_manager = ConfigManager('config.yaml')
config = config_manager.load_config()

# Initialize components
scanner = ImageScanner(config)
processor = ImageProcessor(config)
classifier = ImageClassifier(config)
dataset_manager = DatasetManager(config)

# Process images
image_files = scanner.scan_directory(Path('input_dir'))
processed_data = processor.process_images(image_files)
classified_data = classifier.classify_images(processed_data)
dataset_manager.create_dataset(classified_data, Path('output_dir'))
```

## Troubleshooting

### Common Issues

1. **Memory Issues with Large Datasets:**
   - Reduce `batch_size` in configuration
   - Use `create_symlinks: true` instead of copying files

2. **Slow Processing:**
   - Disable `enhance_quality` for faster processing
   - Reduce `target_size` dimensions
   - Increase `batch_size` if memory allows

3. **Classification Accuracy:**
   - Adjust `confidence_threshold`
   - Add more `custom_categories`
   - Use a different `model_name`

### Performance Tips

- Use SSD storage for better I/O performance
- Adjust batch size based on available RAM
- Enable multiprocessing for large datasets
- Use appropriate image formats (JPEG for photos, PNG for graphics)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For issues and questions:
- Check the troubleshooting section
- Review configuration options
- Submit an issue with detailed information

## Changelog

### Version 1.0.0
- Initial release
- Complete image processing pipeline
- Multi-criteria classification
- Comprehensive analytics
- Multiple export formats
