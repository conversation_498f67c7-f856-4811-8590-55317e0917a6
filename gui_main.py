#!/usr/bin/env python3
"""
GUI Application for Advanced Image Processing and Classification
==============================================================

A user-friendly graphical interface for the image processing application.
"""

import tkinter as tk
from tkinter import ttk, filedialog, messagebox, scrolledtext
import threading
import queue
import sys
import os
from pathlib import Path
import json
import yaml
from datetime import datetime

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / 'src'))

from src.core.scanner import ImageScanner
from src.core.processor import ImageProcessor
from src.core.classifier import ImageClassifier
from src.core.dataset_manager import DatasetManager
from src.utils.config_manager import ConfigManager
from src.utils.logger import setup_logger


class ImageProcessingGUI:
    """Main GUI application class."""
    
    def __init__(self, root):
        self.root = root
        self.root.title("Advanced Image Processing & Classification Tool")
        self.root.geometry("1000x700")
        self.root.minsize(800, 600)
        
        # Initialize variables
        self.input_dir = tk.StringVar()
        self.output_dir = tk.StringVar()
        self.config_file = tk.StringVar(value="config.yaml")
        self.processing_active = False
        self.progress_queue = queue.Queue()
        
        # Configuration variables
        self.target_width = tk.IntVar(value=224)
        self.target_height = tk.IntVar(value=224)
        self.train_ratio = tk.DoubleVar(value=0.7)
        self.val_ratio = tk.DoubleVar(value=0.2)
        self.test_ratio = tk.DoubleVar(value=0.1)
        self.enhance_quality = tk.BooleanVar(value=True)
        self.normalize = tk.BooleanVar(value=True)
        self.convert_grayscale = tk.BooleanVar(value=False)
        self.confidence_threshold = tk.DoubleVar(value=0.5)
        
        # Custom categories
        self.custom_categories = []
        
        # Setup GUI
        self.setup_gui()
        self.setup_logging()
        
        # Start progress monitoring
        self.monitor_progress()
    
    def setup_gui(self):
        """Setup the main GUI layout."""
        # Create main notebook for tabs
        self.notebook = ttk.Notebook(self.root)
        self.notebook.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create tabs
        self.create_main_tab()
        self.create_config_tab()
        self.create_advanced_tab()
        self.create_log_tab()
        
        # Create status bar
        self.create_status_bar()
    
    def create_main_tab(self):
        """Create the main processing tab."""
        main_frame = ttk.Frame(self.notebook)
        self.notebook.add(main_frame, text="Main Processing")
        
        # Input/Output section
        io_frame = ttk.LabelFrame(main_frame, text="Input/Output Settings", padding=10)
        io_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Input directory
        ttk.Label(io_frame, text="Input Directory:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(io_frame, textvariable=self.input_dir, width=50).grid(row=0, column=1, padx=5, pady=2)
        ttk.Button(io_frame, text="Browse", command=self.browse_input_dir).grid(row=0, column=2, padx=5, pady=2)
        
        # Output directory
        ttk.Label(io_frame, text="Output Directory:").grid(row=1, column=0, sticky=tk.W, pady=2)
        ttk.Entry(io_frame, textvariable=self.output_dir, width=50).grid(row=1, column=1, padx=5, pady=2)
        ttk.Button(io_frame, text="Browse", command=self.browse_output_dir).grid(row=1, column=2, padx=5, pady=2)
        
        # Configuration file
        ttk.Label(io_frame, text="Config File:").grid(row=2, column=0, sticky=tk.W, pady=2)
        ttk.Entry(io_frame, textvariable=self.config_file, width=50).grid(row=2, column=1, padx=5, pady=2)
        ttk.Button(io_frame, text="Browse", command=self.browse_config_file).grid(row=2, column=2, padx=5, pady=2)
        
        # Quick settings
        quick_frame = ttk.LabelFrame(main_frame, text="Quick Settings", padding=10)
        quick_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Target size
        size_frame = ttk.Frame(quick_frame)
        size_frame.pack(fill=tk.X, pady=2)
        ttk.Label(size_frame, text="Target Size:").pack(side=tk.LEFT)
        ttk.Entry(size_frame, textvariable=self.target_width, width=8).pack(side=tk.LEFT, padx=5)
        ttk.Label(size_frame, text="x").pack(side=tk.LEFT)
        ttk.Entry(size_frame, textvariable=self.target_height, width=8).pack(side=tk.LEFT, padx=5)
        
        # Split ratios
        split_frame = ttk.Frame(quick_frame)
        split_frame.pack(fill=tk.X, pady=2)
        ttk.Label(split_frame, text="Split Ratios - Train:").pack(side=tk.LEFT)
        ttk.Entry(split_frame, textvariable=self.train_ratio, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Label(split_frame, text="Val:").pack(side=tk.LEFT)
        ttk.Entry(split_frame, textvariable=self.val_ratio, width=8).pack(side=tk.LEFT, padx=2)
        ttk.Label(split_frame, text="Test:").pack(side=tk.LEFT)
        ttk.Entry(split_frame, textvariable=self.test_ratio, width=8).pack(side=tk.LEFT, padx=2)
        
        # Checkboxes
        check_frame = ttk.Frame(quick_frame)
        check_frame.pack(fill=tk.X, pady=2)
        ttk.Checkbutton(check_frame, text="Enhance Quality", variable=self.enhance_quality).pack(side=tk.LEFT, padx=10)
        ttk.Checkbutton(check_frame, text="Normalize", variable=self.normalize).pack(side=tk.LEFT, padx=10)
        ttk.Checkbutton(check_frame, text="Convert to Grayscale", variable=self.convert_grayscale).pack(side=tk.LEFT, padx=10)
        
        # Progress section
        progress_frame = ttk.LabelFrame(main_frame, text="Processing Progress", padding=10)
        progress_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=5)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(progress_frame, variable=self.progress_var, maximum=100)
        self.progress_bar.pack(fill=tk.X, pady=5)
        
        # Status label
        self.status_label = ttk.Label(progress_frame, text="Ready to process images")
        self.status_label.pack(pady=2)
        
        # Statistics display
        self.stats_text = scrolledtext.ScrolledText(progress_frame, height=8, state=tk.DISABLED)
        self.stats_text.pack(fill=tk.BOTH, expand=True, pady=5)
        
        # Control buttons
        button_frame = ttk.Frame(main_frame)
        button_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.start_button = ttk.Button(button_frame, text="Start Processing", command=self.start_processing)
        self.start_button.pack(side=tk.LEFT, padx=5)
        
        self.stop_button = ttk.Button(button_frame, text="Stop Processing", command=self.stop_processing, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=5)
        
        ttk.Button(button_frame, text="Clear Log", command=self.clear_stats).pack(side=tk.LEFT, padx=5)
        ttk.Button(button_frame, text="Save Config", command=self.save_current_config).pack(side=tk.RIGHT, padx=5)
    
    def create_config_tab(self):
        """Create the configuration tab."""
        config_frame = ttk.Frame(self.notebook)
        self.notebook.add(config_frame, text="Configuration")
        
        # Create scrollable frame
        canvas = tk.Canvas(config_frame)
        scrollbar = ttk.Scrollbar(config_frame, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        
        canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # Processing settings
        proc_frame = ttk.LabelFrame(scrollable_frame, text="Processing Settings", padding=10)
        proc_frame.pack(fill=tk.X, padx=10, pady=5)
        
        # Batch size
        ttk.Label(proc_frame, text="Batch Size:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.batch_size = tk.IntVar(value=32)
        ttk.Entry(proc_frame, textvariable=self.batch_size, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Enhancement factors
        ttk.Label(proc_frame, text="Brightness Factor:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.brightness_factor = tk.DoubleVar(value=1.0)
        ttk.Entry(proc_frame, textvariable=self.brightness_factor, width=10).grid(row=1, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(proc_frame, text="Contrast Factor:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.contrast_factor = tk.DoubleVar(value=1.0)
        ttk.Entry(proc_frame, textvariable=self.contrast_factor, width=10).grid(row=2, column=1, sticky=tk.W, padx=5, pady=2)
        
        ttk.Label(proc_frame, text="Sharpness Factor:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.sharpness_factor = tk.DoubleVar(value=1.0)
        ttk.Entry(proc_frame, textvariable=self.sharpness_factor, width=10).grid(row=3, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Classification settings
        class_frame = ttk.LabelFrame(scrollable_frame, text="Classification Settings", padding=10)
        class_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Label(class_frame, text="Confidence Threshold:").grid(row=0, column=0, sticky=tk.W, pady=2)
        ttk.Entry(class_frame, textvariable=self.confidence_threshold, width=10).grid(row=0, column=1, sticky=tk.W, padx=5, pady=2)
        
        # Custom categories
        ttk.Label(class_frame, text="Custom Categories:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.categories_text = tk.Text(class_frame, height=4, width=40)
        self.categories_text.grid(row=1, column=1, columnspan=2, padx=5, pady=2)
        
        # Pack canvas and scrollbar
        canvas.pack(side="left", fill="both", expand=True)
        scrollbar.pack(side="right", fill="y")
    
    def create_advanced_tab(self):
        """Create the advanced settings tab."""
        advanced_frame = ttk.Frame(self.notebook)
        self.notebook.add(advanced_frame, text="Advanced")
        
        # Export settings
        export_frame = ttk.LabelFrame(advanced_frame, text="Export Settings", padding=10)
        export_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.export_csv = tk.BooleanVar(value=True)
        self.export_json = tk.BooleanVar(value=True)
        self.export_yaml = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(export_frame, text="Export CSV", variable=self.export_csv).pack(anchor=tk.W)
        ttk.Checkbutton(export_frame, text="Export JSON", variable=self.export_json).pack(anchor=tk.W)
        ttk.Checkbutton(export_frame, text="Export YAML", variable=self.export_yaml).pack(anchor=tk.W)
        
        # Dataset settings
        dataset_frame = ttk.LabelFrame(advanced_frame, text="Dataset Settings", padding=10)
        dataset_frame.pack(fill=tk.X, padx=10, pady=5)
        
        self.preserve_names = tk.BooleanVar(value=False)
        self.create_symlinks = tk.BooleanVar(value=False)
        
        ttk.Checkbutton(dataset_frame, text="Preserve Original Names", variable=self.preserve_names).pack(anchor=tk.W)
        ttk.Checkbutton(dataset_frame, text="Create Symlinks (instead of copying)", variable=self.create_symlinks).pack(anchor=tk.W)
        
        # Naming pattern
        ttk.Label(dataset_frame, text="Naming Pattern:").pack(anchor=tk.W, pady=(10,0))
        self.naming_pattern = tk.StringVar(value="{category}_{index:06d}")
        ttk.Entry(dataset_frame, textvariable=self.naming_pattern, width=40).pack(anchor=tk.W, pady=2)
        
        # Preset configurations
        preset_frame = ttk.LabelFrame(advanced_frame, text="Preset Configurations", padding=10)
        preset_frame.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(preset_frame, text="Load Photo Config", command=lambda: self.load_preset("examples/photo_config.yaml")).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="Load Document Config", command=lambda: self.load_preset("examples/document_config.yaml")).pack(side=tk.LEFT, padx=5)
        ttk.Button(preset_frame, text="Load Medical Config", command=lambda: self.load_preset("examples/medical_config.yaml")).pack(side=tk.LEFT, padx=5)
    
    def create_log_tab(self):
        """Create the logging tab."""
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="Logs")
        
        # Log display
        self.log_text = scrolledtext.ScrolledText(log_frame, state=tk.DISABLED)
        self.log_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Log controls
        log_controls = ttk.Frame(log_frame)
        log_controls.pack(fill=tk.X, padx=10, pady=5)
        
        ttk.Button(log_controls, text="Clear Logs", command=self.clear_logs).pack(side=tk.LEFT, padx=5)
        ttk.Button(log_controls, text="Save Logs", command=self.save_logs).pack(side=tk.LEFT, padx=5)
    
    def create_status_bar(self):
        """Create the status bar."""
        self.status_bar = ttk.Frame(self.root)
        self.status_bar.pack(fill=tk.X, side=tk.BOTTOM)
        
        self.status_text = ttk.Label(self.status_bar, text="Ready")
        self.status_text.pack(side=tk.LEFT, padx=5)
        
        # Time display
        self.time_label = ttk.Label(self.status_bar, text="")
        self.time_label.pack(side=tk.RIGHT, padx=5)
        self.update_time()
    
    def setup_logging(self):
        """Setup logging to display in GUI."""
        import logging
        
        class GUILogHandler(logging.Handler):
            def __init__(self, text_widget):
                super().__init__()
                self.text_widget = text_widget
            
            def emit(self, record):
                msg = self.format(record)
                def append():
                    self.text_widget.config(state=tk.NORMAL)
                    self.text_widget.insert(tk.END, msg + '\n')
                    self.text_widget.config(state=tk.DISABLED)
                    self.text_widget.see(tk.END)
                self.text_widget.after(0, append)
        
        # Setup GUI log handler
        gui_handler = GUILogHandler(self.log_text)
        gui_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        
        # Get root logger and add handler
        logger = logging.getLogger()
        logger.addHandler(gui_handler)
        logger.setLevel(logging.INFO)

    def browse_input_dir(self):
        """Browse for input directory."""
        directory = filedialog.askdirectory(title="Select Input Directory")
        if directory:
            self.input_dir.set(directory)

    def browse_output_dir(self):
        """Browse for output directory."""
        directory = filedialog.askdirectory(title="Select Output Directory")
        if directory:
            self.output_dir.set(directory)

    def browse_config_file(self):
        """Browse for configuration file."""
        filename = filedialog.askopenfilename(
            title="Select Configuration File",
            filetypes=[("YAML files", "*.yaml *.yml"), ("JSON files", "*.json"), ("All files", "*.*")]
        )
        if filename:
            self.config_file.set(filename)

    def load_preset(self, preset_path):
        """Load a preset configuration."""
        try:
            if Path(preset_path).exists():
                config_manager = ConfigManager(preset_path)
                config = config_manager.load_config()
                self.load_config_to_gui(config)
                self.config_file.set(preset_path)
                self.log_message(f"Loaded preset configuration: {preset_path}")
            else:
                messagebox.showwarning("Warning", f"Preset file not found: {preset_path}")
        except Exception as e:
            messagebox.showerror("Error", f"Failed to load preset: {e}")

    def load_config_to_gui(self, config):
        """Load configuration values into GUI elements."""
        # Basic settings
        target_size = config.get('target_size', [224, 224])
        self.target_width.set(target_size[0])
        self.target_height.set(target_size[1])

        split_ratios = config.get('split_ratios', [0.7, 0.2, 0.1])
        self.train_ratio.set(split_ratios[0])
        self.val_ratio.set(split_ratios[1])
        self.test_ratio.set(split_ratios[2])

        self.enhance_quality.set(config.get('enhance_quality', True))
        self.normalize.set(config.get('normalize', True))
        self.convert_grayscale.set(config.get('convert_to_grayscale', False))

        # Advanced settings
        self.batch_size.set(config.get('batch_size', 32))
        self.brightness_factor.set(config.get('brightness_factor', 1.0))
        self.contrast_factor.set(config.get('contrast_factor', 1.0))
        self.sharpness_factor.set(config.get('sharpness_factor', 1.0))
        self.confidence_threshold.set(config.get('confidence_threshold', 0.5))

        # Custom categories
        categories = config.get('custom_categories', [])
        self.categories_text.delete(1.0, tk.END)
        self.categories_text.insert(1.0, '\n'.join(categories))

        # Export formats
        export_formats = config.get('export_formats', ['csv', 'json'])
        self.export_csv.set('csv' in export_formats)
        self.export_json.set('json' in export_formats)
        self.export_yaml.set('yaml' in export_formats)

        # Dataset settings
        self.preserve_names.set(config.get('preserve_original_names', False))
        self.create_symlinks.set(config.get('create_symlinks', False))
        self.naming_pattern.set(config.get('naming_pattern', '{category}_{index:06d}'))

    def get_config_from_gui(self):
        """Get configuration from GUI elements."""
        # Get custom categories
        categories_text = self.categories_text.get(1.0, tk.END).strip()
        custom_categories = [cat.strip() for cat in categories_text.split('\n') if cat.strip()]

        # Get export formats
        export_formats = []
        if self.export_csv.get():
            export_formats.append('csv')
        if self.export_json.get():
            export_formats.append('json')
        if self.export_yaml.get():
            export_formats.append('yaml')

        config = {
            # Processing settings
            'target_size': [self.target_width.get(), self.target_height.get()],
            'maintain_aspect_ratio': True,
            'convert_to_grayscale': self.convert_grayscale.get(),
            'normalize': self.normalize.get(),
            'enhance_quality': self.enhance_quality.get(),
            'batch_size': self.batch_size.get(),
            'brightness_factor': self.brightness_factor.get(),
            'contrast_factor': self.contrast_factor.get(),
            'sharpness_factor': self.sharpness_factor.get(),
            'noise_reduction': True,

            # Classification settings
            'use_pretrained_model': False,  # Simplified for GUI
            'confidence_threshold': self.confidence_threshold.get(),
            'custom_categories': custom_categories,

            # Dataset settings
            'split_ratios': [self.train_ratio.get(), self.val_ratio.get(), self.test_ratio.get()],
            'naming_pattern': self.naming_pattern.get(),
            'preserve_original_names': self.preserve_names.get(),
            'copy_images': not self.create_symlinks.get(),
            'create_symlinks': self.create_symlinks.get(),
            'export_formats': export_formats,

            # General settings
            'validate_mime_type': True,
            'resume': False,
            'log_level': 'INFO'
        }

        return config

    def save_current_config(self):
        """Save current GUI configuration to file."""
        config = self.get_config_from_gui()
        filename = filedialog.asksaveasfilename(
            title="Save Configuration",
            defaultextension=".yaml",
            filetypes=[("YAML files", "*.yaml"), ("JSON files", "*.json")]
        )
        if filename:
            try:
                config_manager = ConfigManager()
                config_manager.save_config(config, filename)
                self.log_message(f"Configuration saved to: {filename}")
                messagebox.showinfo("Success", "Configuration saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save configuration: {e}")

    def validate_inputs(self):
        """Validate user inputs."""
        if not self.input_dir.get():
            messagebox.showerror("Error", "Please select an input directory")
            return False

        if not self.output_dir.get():
            messagebox.showerror("Error", "Please select an output directory")
            return False

        if not Path(self.input_dir.get()).exists():
            messagebox.showerror("Error", "Input directory does not exist")
            return False

        # Validate split ratios
        total_ratio = self.train_ratio.get() + self.val_ratio.get() + self.test_ratio.get()
        if abs(total_ratio - 1.0) > 0.01:
            messagebox.showerror("Error", "Split ratios must sum to 1.0")
            return False

        return True

    def start_processing(self):
        """Start the image processing pipeline."""
        if not self.validate_inputs():
            return

        if self.processing_active:
            messagebox.showwarning("Warning", "Processing is already active")
            return

        # Update UI state
        self.processing_active = True
        self.start_button.config(state=tk.DISABLED)
        self.stop_button.config(state=tk.NORMAL)
        self.progress_var.set(0)
        self.status_label.config(text="Starting processing...")

        # Start processing in separate thread
        self.processing_thread = threading.Thread(target=self.run_processing)
        self.processing_thread.daemon = True
        self.processing_thread.start()

    def stop_processing(self):
        """Stop the processing (placeholder for now)."""
        self.processing_active = False
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)
        self.status_label.config(text="Processing stopped by user")
        self.log_message("Processing stopped by user")

    def run_processing(self):
        """Run the actual processing pipeline."""
        try:
            # Get configuration
            config = self.get_config_from_gui()

            # Update progress
            self.update_progress(5, "Initializing components...")

            # Initialize components
            scanner = ImageScanner(config)
            processor = ImageProcessor(config)
            classifier = ImageClassifier(config)
            dataset_manager = DatasetManager(config)

            input_path = Path(self.input_dir.get())
            output_path = Path(self.output_dir.get())

            # Step 1: Scan images
            self.update_progress(10, "Scanning for images...")
            image_files = scanner.scan_directory(input_path)
            self.log_message(f"Found {len(image_files)} images")

            if len(image_files) == 0:
                self.update_progress(100, "No images found")
                return

            # Step 2: Process images
            self.update_progress(25, f"Processing {len(image_files)} images...")
            processed_data = processor.process_images(image_files)
            self.log_message(f"Processed {len(processed_data)} images")

            # Step 3: Classify images
            self.update_progress(60, "Classifying images...")
            classified_data = classifier.classify_images(processed_data)
            self.log_message(f"Classified {len(classified_data)} images")

            # Step 4: Create dataset
            self.update_progress(80, "Creating dataset structure...")
            dataset_manager.create_dataset(classified_data, output_path)

            # Display statistics
            self.update_progress(95, "Generating statistics...")
            self.display_statistics(scanner, processor, classifier, dataset_manager)

            self.update_progress(100, "Processing completed successfully!")
            self.log_message("Processing pipeline completed successfully!")

            # Show completion message
            self.root.after(0, lambda: messagebox.showinfo("Success",
                f"Processing completed successfully!\nOutput saved to: {output_path}"))

        except Exception as e:
            error_msg = f"Error during processing: {e}"
            self.log_message(error_msg)
            self.root.after(0, lambda: messagebox.showerror("Error", error_msg))
            self.update_progress(0, "Processing failed")

        finally:
            # Reset UI state
            self.processing_active = False
            self.root.after(0, self.reset_ui_state)

    def update_progress(self, value, status):
        """Update progress bar and status."""
        def update():
            self.progress_var.set(value)
            self.status_label.config(text=status)
            self.status_text.set(status)

        self.root.after(0, update)

    def reset_ui_state(self):
        """Reset UI state after processing."""
        self.start_button.config(state=tk.NORMAL)
        self.stop_button.config(state=tk.DISABLED)

    def display_statistics(self, scanner, processor, classifier, dataset_manager):
        """Display processing statistics."""
        stats_text = f"""
PROCESSING STATISTICS
{'='*50}

Scanner Statistics:
{'-'*20}
{json.dumps(scanner.get_statistics(), indent=2)}

Processor Statistics:
{'-'*20}
{json.dumps(processor.get_statistics(), indent=2)}

Classifier Statistics:
{'-'*20}
{json.dumps(classifier.get_statistics(), indent=2)}

Dataset Statistics:
{'-'*20}
{json.dumps(dataset_manager.get_statistics(), indent=2)}

Processing completed at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
"""

        def update_stats():
            self.stats_text.config(state=tk.NORMAL)
            self.stats_text.delete(1.0, tk.END)
            self.stats_text.insert(1.0, stats_text)
            self.stats_text.config(state=tk.DISABLED)

        self.root.after(0, update_stats)

    def clear_stats(self):
        """Clear statistics display."""
        self.stats_text.config(state=tk.NORMAL)
        self.stats_text.delete(1.0, tk.END)
        self.stats_text.config(state=tk.DISABLED)

    def clear_logs(self):
        """Clear log display."""
        self.log_text.config(state=tk.NORMAL)
        self.log_text.delete(1.0, tk.END)
        self.log_text.config(state=tk.DISABLED)

    def save_logs(self):
        """Save logs to file."""
        filename = filedialog.asksaveasfilename(
            title="Save Logs",
            defaultextension=".txt",
            filetypes=[("Text files", "*.txt"), ("All files", "*.*")]
        )
        if filename:
            try:
                with open(filename, 'w') as f:
                    f.write(self.log_text.get(1.0, tk.END))
                messagebox.showinfo("Success", "Logs saved successfully!")
            except Exception as e:
                messagebox.showerror("Error", f"Failed to save logs: {e}")

    def log_message(self, message):
        """Add message to log."""
        import logging
        logging.info(message)

    def monitor_progress(self):
        """Monitor progress updates from processing thread."""
        try:
            while True:
                message = self.progress_queue.get_nowait()
                # Handle progress updates here if needed
        except queue.Empty:
            pass

        # Schedule next check
        self.root.after(100, self.monitor_progress)

    def update_time(self):
        """Update time display."""
        current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        self.time_label.config(text=current_time)
        self.root.after(1000, self.update_time)


def main():
    """Main function to run the GUI application."""
    # Create root window
    root = tk.Tk()

    # Set application icon (if available)
    try:
        # You can add an icon file here
        # root.iconbitmap('icon.ico')
        pass
    except:
        pass

    # Create and run application
    ImageProcessingGUI(root)

    # Center window on screen
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f'{width}x{height}+{x}+{y}')

    # Start the GUI event loop
    try:
        root.mainloop()
    except KeyboardInterrupt:
        print("Application interrupted by user")
    except Exception as e:
        print(f"Application error: {e}")


if __name__ == "__main__":
    main()
